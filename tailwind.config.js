/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // Phoenix 模板文件
    "./lib/**/*.{ex,exs,heex}",
    "./lib/**/components/**/*.{ex,exs,heex}",
    "./lib/**/live/**/*.{ex,exs,heex}",
    "./lib/**/layouts/**/*.{ex,exs,heex}",
    
    // JavaScript 文件
    "./assets/js/**/*.js",
    
    // CSS 文件（包含图标安全列表）
    "./assets/css/**/*.css",
    
    // Backpex 相关文件
    "./deps/backpex/**/*.{ex,exs,heex}",
    "./local_deps/backpex/**/*.{ex,exs,heex}",
    
    // 其他可能包含类名的文件
    "./priv/**/*.{html,heex}",
  ],
  
  theme: {
    extend: {
      // 扩展主题配置
    }
  },
  
  plugins: [
    // DaisyUI 插件
    require("daisyui"),
    
    // Heroicons 插件
    require("./assets/css/tailwind.heroicons.js"),
  ],
  
  // DaisyUI 配置
  daisyui: {
    themes: ["light", "dark", "cupcake"],
    darkTheme: "dark",
    base: true,
    styled: true,
    utils: true,
    prefix: "",
    logs: true,
    themeRoot: ":root",
  },
  
  // 确保所有 hero- 类都被包含
  safelist: [
    // 基本图标类模式
    {
      pattern: /hero-.*/,
      variants: ['hover', 'focus', 'active'],
    },
    // 尺寸类
    'size-4', 'size-5', 'size-6', 'size-8',
    // 颜色类
    'text-primary', 'text-secondary', 'text-accent', 'text-neutral',
    'text-info', 'text-success', 'text-warning', 'text-error',
  ],
}
